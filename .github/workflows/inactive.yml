name: Close and lock inactive threads
on:
  schedule:
    - cron: "0 8 * * *"
jobs:
  manage-inactive:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          days-before-issue-stale: 30
          days-before-issue-close: 7
          stale-issue-label: inactive
          stale-issue-message: >
            This issue has been automatically marked as inactive 
            due to lack of recent activity. 
            Should you believe it remains unresolved and warrants attention, 
            kindly leave a comment on this thread. 
          exempt-issue-labels: enhancement,discussion
          days-before-pr-stale: -1
          days-before-pr-close: -1
          operations-per-run: 128
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - uses: dessant/lock-threads@v5
        with:
          issue-inactive-days: 30
          issue-comment: >
            This issue has been automatically locked since there
            has not been any recent activity after it was closed.
            Please open a new issue for related bugs.
          pr-inactive-days: 30
          pr-comment: >
            This pull request has been automatically locked since there
            has not been any recent activity after it was closed.
            Please open a new issue for related bugs.
          repo-token: ${{ secrets.GITHUB_TOKEN }}
