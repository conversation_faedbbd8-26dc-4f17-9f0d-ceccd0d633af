name: Badcase Report
description: Report a badcase.
title: "[Badcase]: "
body:
  - type: dropdown
    id: series
    attributes:
      label: Model Series
      description: |
        What series of Qwen models were you running? 
        Please note that there may not be response for previous model series.
      options:
        - Qwen3
        - Qwen2.5
      default: 0
    validations:
      required: true
  - type: input
    attributes:
      label: What are the models used?
      description: |
        Please list the model used, e.g., Qwen3-8B, Qwen3-8B-GGUF, etc.
        Note that we only maintain models at <https://huggingface.co/Qwen> and <https://www.modelscope.com/organization/qwen>.
      placeholder: "ex: Qwen3-xxx"
    validations:
      required: true
  - type: input
    attributes:
      label: What is the scenario where the problem happened?
      description: | 
        Please briefly describe the scenario, including the framework or the platform, 
        e.g., <PERSON><PERSON>, Transformers, Ollama, SGLang, vLLM, Hugging Face Demo, etc.
      placeholder: "ex: Qwen3-8B cannot generate long texts with Transformers."
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Is this badcase known and can it be solved using avaiable techniques?
      description: |
        Please first check if you have followed the usage guide in related documentation and if the badcase is known: 
        either a workaround is avaiable or the badcase has been already reported.
      options:
        - label: I have followed [the GitHub README](https://github.com/QwenLM/Qwen3).
          required: true
        - label: I have checked [the Qwen documentation](https://qwen.readthedocs.io) and cannot find a solution there.
          required: true
        - label: I have checked the documentation of the related framework and cannot find useful information.
          required: true
        - label: I have searched [the issues](https://github.com/QwenLM/Qwen3/issues?q=is%3Aissue) and there is not a similar one.
          required: true
  - type: textarea
    attributes:
      label: Information about environment
      description: |
        Please provide information about you environment, 
        e.g., the software versions and the information on the OS, GPUs, CUDA, and NVIDIA Driver if GPUs are used.
        
        For example:
        - OS: Ubuntu 24.04
        - Python: Python 3.11
        - GPUs: 4 x NVIDIA A20
        - NVIDIA driver: 560 (from `nvidia-smi`)
        - CUDA compiler: 12.4 (from `nvcc -V`)
        - PyTorch: 2.6.0+cu124 (from `python -c "import troch; print(torch.__version__)"`)

        Python packages (from `pip list`)
        ```
        Package                                  Version
        ---------------------------------------- -----------
        accelerate                               0.33.0
        ...
        ```
    validations:
      required: true

  - type: textarea
    attributes:
      label: Description
      description: |
        Please describe the badcase you have encountered.
        The following template is recommended.
        Feel free to modify as you needed.
      value: |
        #### Steps to reproduce

        This happens to Qwen3-xB-xxx and xxx.
        The badcase can be reproduced with the following steps:
        1. ...
        2. ...

        The following example input & output can be used:
        ```
        system: ...
        user: ...
        ...
        ```

        #### Expected results

        The results are expected to be ...

        #### Attempts to fix

        I have tried several ways to fix this, including:
        1. adjusting the sampling parameters, but ...
        2. prompt engineering, but ...

        #### Anything else helpful for investigation

        I find that this problem also happens to ... 
    validations:
      required: true
