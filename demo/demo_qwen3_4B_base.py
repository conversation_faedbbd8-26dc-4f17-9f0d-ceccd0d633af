from transformers import AutoModelForCausalLM, AutoTokenizer, TextIteratorStreamer
import time
import threading
import sys

model_name = "/home/<USER>/Model/LLM/Qwen/Qwen3-4B-Base"

# load the tokenizer and the model
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype="auto",
    device_map="auto",
    attn_implementation="flash_attention_2" 
)

# prepare the model input
prompt = "请使用100字介绍你自己。"
messages = [
    {"role": "user", "content": prompt}
]

# 是否启用思考模式
enable_thinking = True

# 根据思考模式设置不同的生成参数
if enable_thinking:
    # 思考模式使用 Temperature=0.6、TopP=0.95
    temperature = 0.6
    top_p = 0.95
else:
    # 非思考模式使用 Temperature=0.7、TopP=0.8
    temperature = 0.7
    top_p = 0.8

text = tokenizer.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True,
    enable_thinking=enable_thinking  # 切换思考模式
)
model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

# 创建迭代器流式输出器
streamer = TextIteratorStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)

# 记录开始时间
start_time = time.time()

# 在单独的线程中运行生成
generation_kwargs = dict(
    **model_inputs,
    max_new_tokens=32768,
    do_sample=True,
    temperature=temperature,
    top_p=top_p,
    top_k=20,
    use_cache=True,
    streamer=streamer
)

print(f"\n开始生成（高级流式输出）：{'启用思考模式' if enable_thinking else '不启用思考模式'}")
print(f"使用参数: temperature={temperature}, top_p={top_p}")

# 创建并启动生成线程
thread = threading.Thread(target=model.generate, kwargs=generation_kwargs)
thread.start()

# 收集完整的输出
collected_output = []
thinking_mode = True
thinking_content = []
final_content = []

# 从流式输出器中获取生成的文本并实时显示
for new_text in streamer:
    # 检查是否包含思考结束标记
    if "</think>" in new_text and thinking_mode:
        thinking_mode = False
        # 分割思考内容和最终内容
        parts = new_text.split("</think>", 1)
        thinking_content.append(parts[0])
        if len(parts) > 1 and parts[1]:
            final_content.append(parts[1])
        print("\n--- 思考结束，开始输出最终回答 ---\n")
    elif thinking_mode:
        thinking_content.append(new_text)
        print(new_text, end="", flush=True)
    else:
        final_content.append(new_text)
        print(new_text, end="", flush=True)
    
    collected_output.append(new_text)
    sys.stdout.flush()  # 确保输出立即显示

# 等待生成线程完成
thread.join()

# 计算生成时间
generation_time = time.time() - start_time

# 合并思考内容和最终内容
full_thinking_content = "".join(thinking_content).strip()
full_final_content = "".join(final_content).strip()

print("\n\n===== 生成完成 =====")
print("\n思考内容:")
print(full_thinking_content if full_thinking_content else "无思考内容")
print("\n最终回答:")
print(full_final_content)
print(f"\n生成耗时: {generation_time:.2f} 秒")
