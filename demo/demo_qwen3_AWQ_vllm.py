from openai import OpenAI
import sys

openai_api_key = "EMPTY"
openai_api_base = "http://127.0.0.1:8666/v1"

client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

def chat_with_model(messages, model_path, enable_thinking=False):
    """与模型进行对话并流式输出回复"""
    chat_response = client.chat.completions.create(
        model=model_path,
        messages=messages,
        extra_body={
            "chat_template_kwargs": {"enable_thinking": enable_thinking}
        },
        stream=True,
    )
    
    print("回答: \n", end="", flush=True)
    full_response = ""
    for chunk in chat_response:
        if chunk.choices[0].delta.content is not None:
            content = chunk.choices[0].delta.content
            print(content, end="", flush=True)
            full_response += content
    print("\n")
    return full_response

def main():
    model_path = "/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ"
    
    # 初始化对话历史
    messages = [
        {"role": "system", "content": "You are a helpful assistant. Always answer in Chinese."}
    ]
    
    print("=" * 50)
    print("欢迎使用 Qwen3-AWQ-vLLM 对话系统")
    print("输入问题与AI对话，输入 'quit' 或 'exit' 退出，输入 'clear' 清空对话历史")
    print("=" * 50)
    
    while True:
        user_input = input("\n问题: ")
        
        # 处理特殊命令
        if user_input.lower() in ["quit", "exit", "q"]:
            print("再见！")
            break
        
        if user_input.lower() == "clear":
            # 保留系统提示
            messages = [messages[0]]
            print("对话历史已清空！")
            continue
        
        if not user_input.strip():
            continue
        
        # 添加用户输入到对话历史
        messages.append({"role": "user", "content": user_input})
        
        # 获取模型回复
        response = chat_with_model(messages, model_path, enable_thinking=False)
        
        # 将回复添加到对话历史
        messages.append({"role": "assistant", "content": response})

if __name__ == "__main__":
    main()
