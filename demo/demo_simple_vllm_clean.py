import logging
import os
import sys
import time
from tqdm import tqdm
from vllm import LLM, SamplingParams

# 禁用所有相关库的日志输出
logging.getLogger().setLevel(logging.ERROR)  # 设置根日志记录器级别
logging.getLogger("vllm").setLevel(logging.ERROR)
logging.getLogger("flashinfer").setLevel(logging.ERROR)
logging.getLogger("transformers").setLevel(logging.ERROR)

# 禁用vLLM内部进度条
os.environ["VLLM_DISABLE_PROGRESS_BAR"] = "1"

# 临时重定向标准错误输出
class NullWriter:
    def write(self, text): pass
    def flush(self): pass

# 保存原始stderr
original_stderr = sys.stderr
# 创建空输出对象
null_writer = NullWriter()

prompts = [
    "介绍一下你自己。",
    "3.11和3.9哪一个比较大？",
    "straberry这个单词里面有几个r？",
]
sampling_params = SamplingParams(temperature=0.6, top_p=0.95)

# 显示加载模型的进度条
print("\n正在加载模型...")
start_time = time.time()
with tqdm(total=100, desc="加载进度") as pbar:
    pbar.update(20)  # 预更新进度条表示开始加载
    
    # 重定向stderr到空输出
    sys.stderr = null_writer
    
    try:
        llm = LLM(model="/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
                tensor_parallel_size=4,
                gpu_memory_utilization=0.7,
                max_model_len=4096,
                max_num_batched_tokens=4096,
                quantization="awq")
    finally:
        # 恢复stderr
        sys.stderr = original_stderr
    
    pbar.update(80)  # 完成加载后更新到100%

print(f"模型加载完成！耗时 {time.time() - start_time:.2f} 秒\n")

# 使用进度条显示生成过程
print("开始生成文本...")
outputs = []
generation_times = []
total_gen_start_time = time.time()

with tqdm(total=len(prompts), desc="生成进度") as pbar:
    # 重定向stderr到空输出
    sys.stderr = null_writer
    
    try:
        for prompt in prompts:
            prompt_start_time = time.time()
            output = llm.generate([prompt], sampling_params)[0]
            prompt_time = time.time() - prompt_start_time
            
            outputs.append(output)
            generation_times.append(prompt_time)
            pbar.update(1)
    finally:
        # 恢复stderr
        sys.stderr = original_stderr

total_gen_time = time.time() - total_gen_start_time
avg_gen_time = sum(generation_times) / len(generation_times)

# 简洁输出结果
print("\n生成结果:")
print("-" * 60)
for i, (output, gen_time) in enumerate(zip(outputs, generation_times)):
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"提示 {i+1}: {prompt}")
    print(f"回答 {i+1}: {generated_text}")
    print(f"耗时: {gen_time:.2f} 秒")
    print("-" * 60)

# 输出总体统计信息
print(f"\n生成统计:")
print(f"总生成耗时: {total_gen_time:.2f} 秒")
print(f"平均每个提示耗时: {avg_gen_time:.2f} 秒")
print(f"最长生成耗时: {max(generation_times):.2f} 秒")
print(f"最短生成耗时: {min(generation_times):.2f} 秒")
