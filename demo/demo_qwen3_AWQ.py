from transformers import AutoModelForCausalLM, AutoTokenizer, TextStreamer, TextIteratorStreamer
import time
import threading
import sys
import torch

model_name = "/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ"
# model_name = "/home/<USER>/Model/LLM/Qwen/Qwen3-14B-AWQ"
# model_name = "/home/<USER>/Model/LLM/Qwen/Qwen3-32B-AWQ"
# model_name = "/home/<USER>/Model/LLM/Qwen/Qwen3-30B-A3B-AWQ"

# load the tokenizer and the model
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.float16,
    device_map="auto",
    attn_implementation="flash_attention_2" 
)

# prepare the model input
prompt = "请使用100字介绍你自己。"
messages = [
    {"role": "user", "content": prompt}
]

# 是否启用思考模式
enable_thinking = False

# 根据思考模式设置不同的生成参数
if enable_thinking:
    # 思考模式使用 Temperature=0.6、TopP=0.95
    temperature = 0.6
    top_p = 0.95
else:
    # 非思考模式使用 Temperature=0.7、TopP=0.8
    temperature = 0.7
    top_p = 0.8

text = tokenizer.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True,
    enable_thinking=enable_thinking # 切换思考模式
)
model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

# 创建流式输出器
streamer = TextStreamer(tokenizer, skip_prompt=True, skip_special_tokens=True)

# 记录开始时间
start_time = time.time()

print(f"\n开始生成（流式输出）：{'启用思考模式' if enable_thinking else '不启用思考模式'}")
print(f"使用参数: temperature={temperature}, top_p={top_p}")

# conduct text completion with streaming
generated_ids = model.generate(
    **model_inputs,
    max_new_tokens=32768,
    do_sample=True,  # 使用采样而非贪婪解码
    temperature=temperature,  # 控制生成文本的随机性
    top_p=top_p,  # 控制生成文本的多样性
    top_k=20,  # 限制每一步考虑的token数量
    use_cache=True,  # 使用KV缓存加速生成
    streamer=streamer  # 添加流式输出
)
output_ids = generated_ids[0][len(model_inputs.input_ids[0]):].tolist()

# parsing thinking content
try:
    # rindex finding 151668 (</think>)
    index = len(output_ids) - output_ids[::-1].index(151668)
except ValueError:
    index = 0

thinking_content = tokenizer.decode(output_ids[:index], skip_special_tokens=True).strip("\n")
content = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")

# 计算生成时间
generation_time = time.time() - start_time

print("\n\n===== 生成完成 =====")
print("\n思考内容:")
print(thinking_content if thinking_content else "无思考内容")
print("\n最终回答:")
print(content)
print(f"\n生成耗时: {generation_time:.2f} 秒")