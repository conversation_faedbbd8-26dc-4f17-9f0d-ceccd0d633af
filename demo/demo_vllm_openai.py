from openai import OpenAI

openai_api_key = "EMPTY"
openai_api_base = "http://127.0.0.1:8666/v1"

client = OpenAI(
    api_key=openai_api_key,
    base_url=openai_api_base,
)

# 用户问题
user_question = "Tell me a classical joke."
print(f"问题: \n{user_question}\n")

# 创建聊天请求
chat_response = client.chat.completions.create(
    model="/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
    messages=[
        {"role": "system", "content": "You are a helpful assistant. Always answer in Chinese."},
        {"role": "user", "content": user_question},
    ],
    # 添加参数禁用思考模式
    extra_body={
        "chat_template_kwargs": {"enable_thinking": False}
    },
    stream=True,
)

# 流式输出回答内容
print("回答: \n", end="", flush=True)
for chunk in chat_response:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="", flush=True)
print("\n")
