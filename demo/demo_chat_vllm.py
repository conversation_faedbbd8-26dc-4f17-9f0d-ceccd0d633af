#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3 vLLM 多轮对话脚本
支持：自动显存检测 / tensor-parallel 自适应 / 思考模式 / 交互式对话 / 历史记忆
"""

import argparse
import sys
import time
from typing import List, Tuple

import torch
try:
    import GPUtil              # 可选依赖，更方便取显存
except ImportError:
    GPUtil = None

from transformers import AutoTokenizer
from vllm import LLM, SamplingParams


# ---------- GPU 检测 ---------- #
def list_gpu_info() -> Tuple[int, float]:
    """返回 (GPU 数, 单卡最小剩余显存 GiB)"""
    if GPUtil:
        gpus: List = GPUtil.getGPUs()
        if not gpus:
            raise RuntimeError("未检测到 GPU（GPUtil）")
        free_gib = min(gpu.memoryFree for gpu in gpus) / 1024
        return len(gpus), free_gib

    # fallback：torch.cuda
    if not torch.cuda.is_available():
        raise RuntimeError("未检测到 GPU（torch.cuda）")
    cnt = torch.cuda.device_count()
    free_gib = min(torch.cuda.mem_get_info(i)[0] for i in range(cnt)) / 1024**3
    return cnt, free_gib


def pick_tp(user_tp: int | None,
            model_gb: float,
            min_free_gb: float,
            gpu_cnt: int,
            util: float,
            heads: int = 32) -> int:
    """按显存选 tensor_parallel_size（最小可行）
    
    Args:
        user_tp: 用户指定的 tensor_parallel_size
        model_gb: 模型所需显存（单卡）
        min_free_gb: 单卡可用显存
        gpu_cnt: 可用 GPU 数量
        util: 显存利用率
        heads: 模型注意力头数量，确保 tp 是其因子
    """
    if user_tp:                       # 用户显式指定
        return user_tp
        
    # 确保 tp 是 heads 的因子
    valid_tps = [i for i in range(1, gpu_cnt + 1) if heads % i == 0]
    if not valid_tps:
        valid_tps = [1]  # 至少使用单卡
        
    for tp in sorted(valid_tps):
        slice_gb = model_gb / tp
        if slice_gb <= min_free_gb * util * 0.9:
            return tp
    
    # 找不到合适的值，返回最大的合法 tp
    return max(valid_tps) if valid_tps else 1


# ---------- 生成 & 解析 ---------- #
def generate(llm: LLM,
             tokenizer: AutoTokenizer,
             messages: List[dict],
             thinking: bool,
             verbose: bool = False) -> Tuple[str, str, float]:
    """调用 vLLM 生成文本并解析 <think> 内容
    
    Args:
        llm: 加载好的模型
        tokenizer: 分词器
        messages: 对话历史，格式为 [{"role": "user"/"assistant", "content": "..."}]
        thinking: 是否使用思考模式
        verbose: 是否打印生成过程中的原始输出
    
    Returns:
        (回复内容, 思考内容, 耗时)
    """
    temp, top_p = (0.6, 0.95) if thinking else (0.7, 0.8)

    text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True,
        enable_thinking=thinking
    )

    samp = SamplingParams(
        temperature=temp, top_p=top_p, top_k=20,
        max_tokens=4096  # 减小生成的最大token数，与max_model_len保持一致
    )

    print(f"\n[生成开始] 思考模式: {thinking} | temperature={temp} | top_p={top_p}")
    t0 = time.time()
    outputs = llm.generate([text], samp)
    whole = outputs[0].outputs[0].text
    if verbose:
        print(whole)  # 只在详细模式下打印原始输出
    dt = time.time() - t0

    think, ans = "", whole
    s, e = whole.find("<think>"), whole.find("</think>")
    if 0 <= s < e:
        think = whole[s + 7:e].strip()
        ans = whole[e + 8:].strip()

    return ans, think, dt


# ---------- 主程序 ---------- #
def main() -> None:
    parser = argparse.ArgumentParser("Qwen3 vLLM 对话")
    parser.add_argument("--model", default="/home/<USER>/Model/LLM/Qwen/Qwen3-8B",
                        help="模型或权重路径")
    parser.add_argument("--tp", type=int, default=4, help="tensor_parallel_size")
    parser.add_argument("--gpu-mem", type=float, default=0.5,
                        help="每卡显存利用率上限 (0~1)")
    parser.add_argument("--thinking", action="store_true", default=False,
                        help="输出 <think>…</think> 内容")
    parser.add_argument("--quant", choices=["awq", "gptq", "none"],
                        default="none", help="量化类型")
    parser.add_argument("--system", type=str, default="你是一个有用的人工智能助手。",
                        help="系统提示词")
    parser.add_argument("--heads", type=int, default=32,
                        help="模型注意力头数量，Qwen3-8B为32，Qwen3-1.5B为16")
    parser.add_argument("--verbose", action="store_true", default=False,
                        help="是否显示模型生成过程的原始输出")
    args = parser.parse_args()

    # ---- GPU & TP ---- #
    try:
        gpu_cnt, free_gb = list_gpu_info()
        # Qwen3-8B FP16 约 16 GiB；若用 AWQ/GPTQ 可改成 4-6 GiB
        model_gb = 16.0 if args.quant == "none" else 6.0
        tp = pick_tp(args.tp, model_gb, free_gb, gpu_cnt, args.gpu_mem, args.heads)

        print(f"检测到 {gpu_cnt} 张 GPU，最小剩余显存 ≈ {free_gb:.1f} GiB/卡")
        print(f"tensor_parallel_size = {tp} | gpu_memory_utilization = {args.gpu_mem}")
        print(f"注意力头数量 = {args.heads} (确保 TP 是其因子)")

        # ---- Tokenizer ---- #
        print("\n加载分词器...")
        tok = AutoTokenizer.from_pretrained(args.model, trust_remote_code=True)

        # ---- LLM ---- #
        print("加载模型中，这可能需要一些时间...")
        llm = LLM(
            model=args.model,
            dtype="half",
            quantization=None if args.quant == "none" else args.quant,
            tensor_parallel_size=tp,
            max_model_len=4096,  # 减小上下文长度，避免显存不足
            gpu_memory_utilization=args.gpu_mem,
        )
        print("模型加载完成！")
        
        # 初始化对话历史
        history = []
        if args.system:
            history.append({"role": "system", "content": args.system})
        
        # ---- 交互式聊天循环 ---- #
        print("\n" + "="*50)
        print("欢迎使用 Qwen3 对话系统！")
        print("输入内容与模型对话，输入 'q' 或 'quit' 或 'exit' 退出")
        print("输入 'clear' 清空对话历史")
        if args.verbose:
            print("已开启详细模式，将显示模型原始输出")
        if args.thinking:
            print("已开启思考模式，将显示模型思考过程")
        print("="*50 + "\n")

        while True:
            try:
                user_input = input("\n用户输入 >>> ")
                
                # 处理特殊命令
                if user_input.lower() in ["q", "quit", "exit"]:
                    print("\n感谢使用，再见！")
                    break
                
                if user_input.lower() == "clear":
                    # 仅保留系统提示(如果有)
                    if args.system:
                        history = [{"role": "system", "content": args.system}]
                    else:
                        history = []
                    print("\n对话历史已清空！")
                    continue
                
                if not user_input.strip():
                    continue
                    
                # 添加用户输入到对话历史
                history.append({"role": "user", "content": user_input})
                
                # 生成回答
                answer, thinking, sec = generate(llm, tok, history, args.thinking, args.verbose)
                
                # 添加助手回答到对话历史
                history.append({"role": "assistant", "content": answer})
                
                # 输出结果
                print("\n" + "-"*50)
                if thinking:
                    print("\n思考内容:", thinking)
                print("\n回答:")
                print(answer)
                print(f"\n[耗时 {sec:.2f} 秒]")
                print("-"*50)
                
            except KeyboardInterrupt:
                print("\n检测到中断信号，退出程序...")
                break
            except Exception as e:
                print(f"\n发生错误: {e}")
                print("继续对话...")
    
    except ValueError as e:
        if "attention heads" in str(e) and "divisible by tensor parallel size" in str(e):
            print(f"\n错误: {e}")
            print("\n提示: 请指定合适的 tensor_parallel_size，确保其是注意力头数量的因子。")
            print("对于 Qwen3-8B (32头)，可用的 TP 值有: 1, 2, 4, 8, 16, 32")
            print("对于 Qwen3-1.5B (16头)，可用的 TP 值有: 1, 2, 4, 8, 16")
            print("使用示例: python demo_chat_vllm.py --tp 4")
        else:
            print(f"\n发生错误: {e}")
    except Exception as e:
        print(f"\n发生错误: {e}")


if __name__ == "__main__":
    main()

# 使用示例:
# 1. 基本对话: python demo_chat_vllm.py
# 2. 指定TP值防止错误: python demo_chat_vllm.py --tp 4
# 3. 使用思考模式: python demo_chat_vllm.py --thinking
# 4. 显示原始输出: python demo_chat_vllm.py --verbose
# 5. 自定义系统提示: python demo_chat_vllm.py --system "你是医疗领域的专家助手"
# 6. 使用量化模型: python demo_chat_vllm.py --quant awq
