import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

# 模型路径
model_name = "/home/<USER>/Model/LLM/Qwen/QwenLong-L1-32B"

def load_model():
    """加载QwenLong-L1-32B模型和分词器"""
    print(f"正在加载模型: {model_name}")

    # 加载分词器
    print("加载分词器...")
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True
    )

    # 加载模型
    print("加载模型...")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,  # 使用bfloat16以节省显存
        device_map="auto",  # 自动分配设备
        trust_remote_code=True,
        attn_implementation="flash_attention_2"  # 使用FlashAttention-2提升性能
    )

    print("模型加载完成!")
    print(f"模型设备: {model.device}")
    print(f"模型数据类型: {model.dtype}")

    return model, tokenizer

def generate_text(model, tokenizer, prompt, max_length=512):
    """生成文本"""
    print(f"输入提示: {prompt}")

    # 编码输入
    inputs = tokenizer(prompt, return_tensors="pt")
    inputs = {k: v.to(model.device) for k, v in inputs.items()}

    # 生成文本
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.8,
            pad_token_id=tokenizer.eos_token_id
        )

    # 解码输出
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # 只返回新生成的部分
    new_text = generated_text[len(prompt):]

    return new_text

if __name__ == "__main__":
    # 加载模型
    model, tokenizer = load_model()

    # 测试生成
    prompt = "你好，请介绍一下自己。"
    generated = generate_text(model, tokenizer, prompt)

    print(f"\n生成的文本:\n{generated}")

    # 显示模型信息
    print(f"\n模型参数量: {model.num_parameters():,}")
    print(f"词汇表大小: {tokenizer.vocab_size:,}")