from vllm import LLM, SamplingParams

prompts = [
    "Hello, my name is",
    "The president of the United States is",
    "The capital of France is",
    "The future of AI is",
]
sampling_params = SamplingParams(temperature=0.6, top_p=0.95)

llm = LLM(model="/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
          tensor_parallel_size=4,
          gpu_memory_utilization=0.5,
          quantization="awq")
outputs = llm.generate(prompts, sampling_params)

for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")
