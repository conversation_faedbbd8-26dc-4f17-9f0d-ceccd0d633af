import os
# 必须在导入torch之前设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 使用第二块GPU

import torch
from unsloth import FastLanguageModel

# 设置当前设备
torch.cuda.set_device(0)  # 由于CUDA_VISIBLE_DEVICES="1"，这里0对应实际的GPU 1

# 2. 模型参数配置
max_seq_length = 2048  # 最大序列长度
lora_rank = 32        # LoRA的秩

# 3. 加载预训练模型
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name="/home/<USER>/Model/LLM/Qwen/Qwen3-1.7B-Base",
    max_seq_length=max_seq_length,
    load_in_4bit=False,
    fast_inference=True,
    max_lora_rank=lora_rank,
    gpu_memory_utilization=0.7,
    device_map={"": "cuda:0"},
    torch_dtype=torch.float16,
)

# 4. 配置LoRA微调
model = FastLanguageModel.get_peft_model(
    model,
    r=lora_rank,
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj",
    ],
    lora_alpha=lora_rank * 2,
    use_gradient_checkpointing="unsloth",
    random_state=3407,
)