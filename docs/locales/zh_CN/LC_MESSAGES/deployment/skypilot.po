# Copyright (C) 2024, Qwen Team, Alibaba Group.
# This file is distributed under the same license as the Qwen package.
#
msgid ""
msgstr ""
"Project-Id-Version: Qwen \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-28 19:42+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../Qwen/source/deployment/skypilot.rst:2 795ad4f30e27494d93675f71bb1a5cc4
msgid "SkyPilot"
msgstr ""

#: ../../Qwen/source/deployment/skypilot.rst:5 aad807db94a24d868c9c1b364b47e152
msgid "To be updated for Qwen3."
msgstr "仍需为Qwen3更新。"

#: ../../Qwen/source/deployment/skypilot.rst:8 d6bbf736584f4bbfa9c300d50a2ed669
msgid "What is SkyPilot"
msgstr "SkyPilot 是什么"

#: ../../Qwen/source/deployment/skypilot.rst:10
#: b66facae41bf493880e43044e2915a45
msgid "SkyPilot is a framework for running LLMs, AI, and batch jobs on any cloud, offering maximum cost savings, the highest GPU availability, and managed execution. Its features include:"
msgstr "SkyPilot 是一个可以在任何云上运行 LLM 、 AI 应用以及批量任务的框架，旨在实现最大程度的成本节省、最高的 GPU 可用性以及受管理的执行过程。其特性包括："

#: ../../Qwen/source/deployment/skypilot.rst:14
#: 621f021163c549d0aadb1c911a3a3ef5
msgid "Get the best GPU availability by utilizing multiple resources pools across multiple regions and clouds."
msgstr "通过跨区域和跨云充分利用多个资源池，以获得最佳的 GPU 可用性。"

#: ../../Qwen/source/deployment/skypilot.rst:16
#: ea1723c3b5be454cad3219836f4386d8
msgid "Pay absolute minimum — SkyPilot picks the cheapest resources across regions and clouds. No managed solution markups."
msgstr "把费用降到最低—— SkyPilot 在各区域和云平台中为您挑选最便宜的资源。无需任何托管解决方案的额外加价。"

#: ../../Qwen/source/deployment/skypilot.rst:18
#: e479693ecf08411ca35d8d0727c8f441
msgid "Scale up to multiple replicas across different locations and accelerators, all served with a single endpoint"
msgstr "将服务扩展到多个副本上，所有副本通过单一 endpoint 对外提供服务"

#: ../../Qwen/source/deployment/skypilot.rst:20
#: 1f9cdd2ae2544d1faa8a4c463ee0e42c
msgid "Everything stays in your cloud account (your VMs & buckets)"
msgstr "所有内容均保存在您的云账户中（包括您的虚拟机和 bucket ）"

#: ../../Qwen/source/deployment/skypilot.rst:21
#: 5bb9b617764942d989e5093463a359f0
msgid "Completely private - no one else sees your chat history"
msgstr "完全私密 - 没有其他人能看到您的聊天记录"

#: ../../Qwen/source/deployment/skypilot.rst:24
#: cf0c456ac72f40ac98790c11dc243317
msgid "Install SkyPilot"
msgstr "安装 SkyPilot"

#: ../../Qwen/source/deployment/skypilot.rst:26
#: 78d86c1fa8104b138b01aed640b262fc
msgid "We advise you to follow the `instruction <https://skypilot.readthedocs.io/en/latest/getting-started/installation.html>`__ to install SkyPilot. Here we provide a simple example of using ``pip`` for the installation as shown below."
msgstr "我们建议您按照 `指示 <https://skypilot.readthedocs.io/en/latest/getting-started/installation.html>`__ 安装 SkyPilot 。以下为您提供了一个使用 ``pip`` 进行安装的简单示例："

#: ../../Qwen/source/deployment/skypilot.rst:38
#: a7c88265bf404f55b85388c81a240199
msgid "After that, you need to verify cloud access with a command like:"
msgstr "随后，您需要用如下命令确认是否能使用云："

#: ../../Qwen/source/deployment/skypilot.rst:44
#: 72025dfba0144f63a720f6da0dd39bfa
msgid "For more information, check the `official document <https://skypilot.readthedocs.io/en/latest/getting-started/installation.html>`__ and see if you have set up your cloud accounts correctly."
msgstr "若需更多信息，请查阅官方文档，确认您的云账户设置是否正确无误。"

#: ../../Qwen/source/deployment/skypilot.rst:47
#: 61be006061554e5ea40d55497e11e192
msgid "Alternatively, you can also use the official docker image with SkyPilot master branch automatically cloned by running:"
msgstr "或者，您也可以使用官方提供的 docker 镜像，可以自动克隆 SkyPilot 的主分支："

#: ../../Qwen/source/deployment/skypilot.rst:63
#: 4ae89fb44c6643a3a82fca5cee622af4
msgid "Running Qwen2.5-72B-Instruct with SkyPilot"
msgstr "使用 SkyPilot 运行 Qwen2.5-72B-Instruct "

#: ../../Qwen/source/deployment/skypilot.rst:65
#: 1bc4973c2eb745689ded0af54ba33e0e
msgid "Start serving Qwen2.5-72B-Instruct on a single instance with any available GPU in the list specified in `serve-72b.yaml <https://github.com/skypilot-org/skypilot/blob/master/llm/qwen/serve-72b.yaml>`__ with a vLLM-powered OpenAI-compatible endpoint:"
msgstr "`serve-72b.yaml <https://github.com/skypilot-org/skypilot/blob/master/llm/qwen/serve-72b.yaml>`__ 中列出了支持的 GPU 。您可使用配备这类 GPU 的单个运算实例来部署 Qwen2.5-72B-Instruct 服务。该服务由 vLLM 搭建，并与 OpenAI API 兼容。以下为部署方法："

#: ../../Qwen/source/deployment/skypilot.rst:74
#: ../../Qwen/source/deployment/skypilot.rst:123
#: ac3692ed16974facbd58b6886cd111af b325de015e7b4bb0a91491d3f7418792
msgid "**Before launching, make sure you have changed Qwen/Qwen2-72B-Instruct to Qwen/Qwen2.5-72B-Instruct in the YAML file.**"
msgstr "**在启动之前，请先将 YAML 文件中的 Qwen/Qwen2-72B-Instruct 修改为 Qwen/Qwen2.5-72B-Instruct。**"

#: ../../Qwen/source/deployment/skypilot.rst:76
#: 6046b3c86fae4a43878fbadbeb33fbd8
msgid "Send a request to the endpoint for completion:"
msgstr "向该 endpoint 发送续写请求："

#: ../../Qwen/source/deployment/skypilot.rst:90
#: 2ec56c2028a94f568fd2c1a65063d25a
msgid "Send a request for chat completion:"
msgstr "向该 endpoint 发送对话续写请求"

#: ../../Qwen/source/deployment/skypilot.rst:112
#: c8e140ddfd914ff5a460621a7ca1891e
msgid "Scale up the service with SkyPilot Serve"
msgstr "使用 SkyPilot Serve 扩展服务规模"

#: ../../Qwen/source/deployment/skypilot.rst:114
#: 0db304ab396d45adb6017d78cd1ee4a2
msgid "With `SkyPilot Serve <https://skypilot.readthedocs.io/en/latest/serving/sky-serve.html>`__, a serving library built on top of SkyPilot, scaling up the Qwen service is as simple as running:"
msgstr "使用 `SkyPilot Serve <https://skypilot.readthedocs.io/en/latest/serving/sky-serve.html>`__ 扩展 Qwen 的服务规模非常容易，只需运行："

#: ../../Qwen/source/deployment/skypilot.rst:125
#: 25bbbf9e49be44d3899074ff97202d71
msgid "This will start the service with multiple replicas on the cheapest available locations and accelerators. SkyServe will automatically manage the replicas, monitor their health, autoscale based on load, and restart them when needed."
msgstr "这将启动服务，使用多个副本部署在最经济的可用位置和加速器上。 SkyServe 将自动管理这些副本，监控其健康状况，根据负载进行自动伸缩，并在必要时重启它们。"

#: ../../Qwen/source/deployment/skypilot.rst:130
#: bda628bab7ef41a0918dc4b80a9b3cfe
msgid "A single endpoint will be returned and any request sent to the endpoint will be routed to the ready replicas."
msgstr "将返回一个 endpoint ，所有发送至该endpoint的请求都将被路由至就绪状态的副本。"

#: ../../Qwen/source/deployment/skypilot.rst:133
#: b232dbbdcf674d56bcf9c0331c020864
msgid "To check the status of the service, run:"
msgstr "运行如下命令检查服务的状态："

#: ../../Qwen/source/deployment/skypilot.rst:139
#: 556b854caf7243fb93f253ebe2dc9033
msgid "After a while, you will see the following output:"
msgstr "很快，您将看到如下输出："

#: ../../Qwen/source/deployment/skypilot.rst:152
#: 5a6055c5a42c4b2db6693c1095688de8
msgid "As shown, the service is now backed by 2 replicas, one on Azure and one on GCP, and the accelerator type is chosen to be **the cheapest available one** on the clouds. That said, it maximizes the availability of the service while minimizing the cost."
msgstr "如下所示：该服务现由两个副本提供支持，一个位于 Azure 平台，另一个位于 GCP 平台。同时，已为服务选择云服务商提供的 **最经济实惠** 的加速器类型。这样既最大限度地提升了服务的可用性，又尽可能降低了成本。"

#: ../../Qwen/source/deployment/skypilot.rst:157
#: a18533d33dc54a1091ded0b4bba0a1eb
msgid "To access the model, we use a ``curl -L`` command (``-L`` to follow redirect) to send the request to the endpoint:"
msgstr "要访问模型，我们使用带有 ``curl -L`` （用于跟随重定向），将请求发送到 endpoint ："

#: ../../Qwen/source/deployment/skypilot.rst:182
#: 34cd50fd79e24d8895075f7841b025e4
msgid "Accessing Qwen2.5 with Chat GUI"
msgstr "使用 Chat GUI 调用 Qwen2.5"

#: ../../Qwen/source/deployment/skypilot.rst:184
#: ca6994cda1cb469e83ce8c026bb67e42
msgid "It is also possible to access the Qwen2.5 service with GUI by connecting a `FastChat GUI server <https://github.com/lm-sys/FastChat>`__ to the endpoint launched above (see `gui.yaml <https://github.com/skypilot-org/skypilot/blob/master/llm/qwen/gui.yaml>`__)."
msgstr "可以通过 `FastChat <https://github.com/lm-sys/FastChat>`__ 来使用 GUI 调用 Qwen2.5 的服务："

#: ../../Qwen/source/deployment/skypilot.rst:188
#: 99a63e55ab5c46258c20ab89cdfa39dc
msgid "Start the Chat Web UI:"
msgstr "开启一个 Chat Web UI"

#: ../../Qwen/source/deployment/skypilot.rst:194
#: e61593a092c146f8a06af896d6af17f2
msgid "**Before launching, make sure you have changed Qwen/Qwen1.5-72B-Chat to Qwen/Qwen2.5-72B-Instruct in the YAML file.**"
msgstr "**在启动之前，请先将 YAML 文件中的 Qwen/Qwen1.5-72B-Chat 修改为 Qwen/Qwen2.5-72B-Instruct。**"

#: ../../Qwen/source/deployment/skypilot.rst:196
#: 9631068a8b424aa8af6dc6911daac7a9
msgid "Then, we can access the GUI at the returned gradio link:"
msgstr "随后，我们可以通过返回的 gradio 链接来访问 GUI ："

#: ../../Qwen/source/deployment/skypilot.rst:202
#: 1464a56dcd06404aafbe6d7d2c72212b
msgid "Note that you may get better results by using a different temperature and top_p value."
msgstr "你可以通过使用不同的温度和 top_p 值来尝试取得更好的结果。"

#: ../../Qwen/source/deployment/skypilot.rst:205
#: d257f49d835e4c12b28bc680bb78a9cb
msgid "Summary"
msgstr "总结"

#: ../../Qwen/source/deployment/skypilot.rst:207
#: 06b9684a19774eaba4f69862332c5166
msgid "With SkyPilot, it is easy for you to deploy Qwen2.5 on any cloud. We advise you to read the official doc for more usages and updates. Check `this <https://skypilot.readthedocs.io/>`__ out!"
msgstr "通过 SkyPilot ，你可以轻松地在任何云上部署 Qwen2.5 。我们建议您阅读 `官方文档 <https://skypilot.readthedocs.io/>`__ 了解更多用法和最新进展。"

