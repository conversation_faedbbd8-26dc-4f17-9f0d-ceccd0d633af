# Copyright (C) 2024, Qwen Team, Alibaba Group.
# This file is distributed under the same license as the Qwen package.
#
msgid ""
msgstr ""
"Project-Id-Version: Qwen \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-28 19:42+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../Qwen/source/quantization/awq.md:1 363514c3e24c4d2aa54832e85acf34ef
msgid "AWQ"
msgstr "AWQ"

#: ../../Qwen/source/quantization/awq.md:4 36b5c0de1013499f9f1e41edf8fa28ca
msgid "To be updated for Qwen3."
msgstr "仍需为Qwen3更新。"

#: ../../Qwen/source/quantization/awq.md:7 9d6a80a82b044628bc9c911785ac9160
msgid "For quantized models, one of our recommendations is the usage of [AWQ](https://arxiv.org/abs/2306.00978) with [AutoAWQ](https://github.com/casper-hansen/AutoAWQ)."
msgstr "对于量化模型，我们推荐使用 [AWQ](https://arxiv.org/abs/2306.00978) 结合 [AutoAWQ](https://github.com/casper-hansen/AutoAWQ) "

#: ../../Qwen/source/quantization/awq.md:9 139542ed4b414cfb834b3fd81ea88d51
msgid "**AWQ** refers to Activation-aware Weight Quantization, a hardware-friendly approach for LLM low-bit weight-only quantization."
msgstr "**AWQ**即激活值感知的权重量化(Activation-aware Weight Quantization)，是一种针对LLM的低比特权重量化的硬件友好方法。"

#: ../../Qwen/source/quantization/awq.md:11 9a2959bb9f984e36a299bc40abca9402
msgid "**AutoAWQ** is an easy-to-use Python library for 4-bit quantized models.  AutoAWQ speeds up models by 3x and reduces memory requirements by 3x compared to FP16.  AutoAWQ implements the Activation-aware Weight Quantization (AWQ) algorithm for quantizing LLMs."
msgstr "**AutoAWQ**是一个易于使用的工具包，用于4比特量化模型。相较于FP16，AutoAWQ能够将模型的运行速度提升3倍，并将内存需求降低至原来的三分之一。AutoAWQ实现了AWQ算法，可用于LLM的量化处理。"

#: ../../Qwen/source/quantization/awq.md:15 4f9fcd93d1f44b48869224c0f4e8b76a
msgid "In this document, we show you how to use the quantized model with Hugging Face `transformers` and also how to quantize your own model."
msgstr "在本文档中，我们将向您展示如何在Hugging Face `transformers`框架下使用量化模型，以及如何对您自己的模型进行量化"

#: ../../Qwen/source/quantization/awq.md:17 870ebc162f3749b48fe454df85aaaf4b
msgid "Usage of AWQ Models with Hugging Face transformers"
msgstr "在Hugging Face transformers中使用AWQ量化模型"

#: ../../Qwen/source/quantization/awq.md:19 cc7bd785c7ac45a4980fbda683699e43
msgid "Now, `transformers` has officially supported AutoAWQ, which means that you can directly use the quantized model with `transformers`.  The following is a very simple code snippet showing how to run `Qwen2.5-7B-Instruct-AWQ` with the quantized model:"
msgstr "现在，`transformers`已经正式支持AutoAWQ，这意味着您可以直接在`transformers`中使用AWQ量化模型。以下是一个非常简单的代码片段，展示如何运行量化模型 `Qwen2.5-7B-Instruct-AWQ` ："

#: ../../Qwen/source/quantization/awq.md:56 47826d51abf54ad8a89ef9b91127a700
msgid "Usage of AWQ  Models with vLLM"
msgstr "在vLLM中使用AWQ量化模型"

#: ../../Qwen/source/quantization/awq.md:58 b7235ae8f8344dd4a3d2029bbe7a40fc
msgid "vLLM has supported AWQ, which means that you can directly use our provided AWQ models or those quantized with `AutoAWQ` with vLLM. We recommend using the latest version of vLLM (`vllm>=0.6.1`) which brings performance improvements to AWQ models; otherwise, the performance might not be well-optimized."
msgstr "vLLM已支持AWQ，您可以直接使用我们提供的AWQ量化模型或使用`AutoAWQ`量化的模型。我们建议使用最新版的vLLM (`vllm>=0.6.1`)，新版为AWQ量化模型提升了效率提；不然推理效率可能并为被良好优化（即效率可能较非量化模型低）。"

#: ../../Qwen/source/quantization/awq.md:61 940ce8fdb5da442b99af2bc1739911c6
msgid "Actually, the usage is the same with the basic usage of vLLM.  We provide a simple example of how to launch OpenAI-API compatible API with vLLM and `Qwen2.5-7B-Instruct-AWQ`:"
msgstr "实际上，使用AWQ模型与vLLM的基本用法相同。我们提供了一个简单的示例，展示了如何通过vLLM启动与OpenAI API兼容的接口，并使用 `Qwen2.5-7B-Instruct-AWQ` 模型："

#: ../../Qwen/source/quantization/awq.md:64 2d249915352049a6a8d5a06e1f4682ee
msgid "Run the following in a shell to start an OpenAI-compatible API service:"
msgstr "在终端中运行以下命令以开启OpenAI兼容API："

#: ../../Qwen/source/quantization/awq.md:70 be7bfbb81698429cbfcbcd24d062fc08
msgid "Then, you can call the API as"
msgstr "随后，您可以这样调用API："

#: ../../Qwen/source/quantization/awq.md:86 0dff7d5c7b044548a82e0ba68a043d80
msgid "or you can use the API client with the `openai` Python package as shown below:"
msgstr "或者你可以按照下面所示的方式，使用 `openai` Python包中的API客户端："

#: ../../Qwen/source/quantization/awq.md:115 65f4d60502ee486382e9bda9a5a826bb
msgid "Quantize Your Own Model with AutoAWQ"
msgstr "使用AutoAWQ量化你的模型"

#: ../../Qwen/source/quantization/awq.md:117 c7c42af91c1a419194d65200bcfa2f26
#, fuzzy
msgid "If you want to quantize your own model to AWQ quantized models, we advise you to use AutoAWQ."
msgstr "如果您希望将自定义模型量化为AWQ量化模型，我们建议您使用AutoAWQ。推荐通过安装源代码来获取并安装该工具包的最新版本："

#: ../../Qwen/source/quantization/awq.md:123 232e94883d044030b2193392788b9314
msgid "Suppose you have finetuned a model based on `Qwen2.5-7B`, which is named `Qwen2.5-7B-finetuned`, with your own dataset, e.g., Alpaca.  To build your own AWQ quantized model, you need to use the training data for calibration.  Below, we provide a simple demonstration for you to run:"
msgstr "假设你已经基于 `Qwen2.5-7B` 模型进行了微调，并将其命名为 `Qwen2.5-7B-finetuned` ，且使用的是你自己的数据集，比如Alpaca。若要构建你自己的AWQ量化模型，你需要使用训练数据进行校准。以下，我们将为你提供一个简单的演示示例以便运行："

#: ../../Qwen/source/quantization/awq.md:141 5162195f32ee4ecba229aa137da1aba4
msgid "Then you need to prepare your data for calibration.  What you need to do is just put samples into a list, each of which is a text.  As we directly use our finetuning data for calibration, we first format it with ChatML template.  For example,"
msgstr "接下来，您需要准备数据以进行校准。您需要做的就是将样本放入一个列表中，其中每个样本都是一段文本。由于我们直接使用微调数据来进行校准，所以我们首先使用ChatML模板对其进行格式化。例如："

#: ../../Qwen/source/quantization/awq.md:153 0d4736e90e0242a8be15533de3aab6ff
msgid "where each `msg` is a typical chat message as shown below:"
msgstr "其中每个 `msg` 是一个典型的聊天消息，如下所示："

#: ../../Qwen/source/quantization/awq.md:163 79d86630600945ac85dbe13d07987016
msgid "Then just run the calibration process by one line of code:"
msgstr "然后只需通过一行代码运行校准过程："

#: ../../Qwen/source/quantization/awq.md:169 1ae219a50508465b98e3b3398e631681
msgid "Finally, save the quantized model:"
msgstr "最后，保存量化模型："

#: ../../Qwen/source/quantization/awq.md:176 58316c1a4172418aba9f37925963e17f
msgid "Then you can obtain your own AWQ quantized model for deployment.  Enjoy!"
msgstr "然后你就可以得到一个可以用于部署的AWQ量化模型。玩得开心！"

