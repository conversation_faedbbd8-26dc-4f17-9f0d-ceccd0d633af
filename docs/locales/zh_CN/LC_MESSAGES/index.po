# Copyright (C) 2024, Qwen Team, Alibaba Group.
# This file is distributed under the same license as the Qwen package.
#
msgid ""
msgstr ""
"Project-Id-Version: Qwen \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-28 19:42+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../Qwen/source/index.rst:34
msgid "Getting Started"
msgstr "快速开始"

#: ../../Qwen/source/index.rst:44
msgid "Inference"
msgstr "推理"

#: ../../Qwen/source/index.rst:51
msgid "Run Locally"
msgstr "本地运行"

#: ../../Qwen/source/index.rst:60
msgid "Deployment"
msgstr "部署"

#: ../../Qwen/source/index.rst:71
msgid "Quantization"
msgstr "量化"

#: ../../Qwen/source/index.rst:80
msgid "Training"
msgstr "训练"

#: ../../Qwen/source/index.rst:87
msgid "Framework"
msgstr "框架"

#: ../../Qwen/source/index.rst:2 6e52d3a497924f828d4c6b9dd59370d5
msgid "Welcome to Qwen!"
msgstr "欢迎来到Qwen"

#: ../../Qwen/source/index.rst:4 235805a6d4a34184821c0f4f81020ef1
msgid "Qwen3"
msgstr ""

#: ../../Qwen/source/index.rst:11 b8a3aa3f31594232959a08d89e9dc7db
msgid "Qwen is the large language model and large multimodal model series of the Qwen Team, Alibaba Group. Both language models and multimodal models are pretrained on large-scale multilingual and multimodal data and post-trained on quality data for aligning to human preferences. Qwen is capable of natural language understanding, text generation, vision understanding, audio understanding, tool use, role play, playing as AI agent, etc."
msgstr "Qwen是阿里巴巴集团Qwen团队研发的大语言模型和大型多模态模型系列。无论是语言模型还是多模态模型，均在大规模多语言和多模态数据上进行预训练，并通过高质量数据进行后期微调以贴近人类偏好。Qwen具备自然语言理解、文本生成、视觉理解、音频理解、工具使用、角色扮演、作为AI Agent进行互动等多种能力。"

#: ../../Qwen/source/index.rst:14 8735c67355064a97b2793b721a701b21
msgid "The latest version, Qwen3, has the following features:"
msgstr "最新版本Qwen3有以下特点："

#: ../../Qwen/source/index.rst:16 1956d75084244379aad9503fcc572f00
msgid "**Dense and Mixture-of-Experts (MoE) models**, available in 0.6B, 1.7B, 4B, 8B, 14B, 32B and 30B-A3B, 235B-A22B."
msgstr "**全尺寸稠密与混合专家模型**：0.6B, 1.7B, 4B, 8B, 14B, 32B and 30B-A3B, 235B-A22B"

#: ../../Qwen/source/index.rst:17 1fdf12161cd14663b67b2c08f9219ddb
msgid "**Seamless switching between thinking mode** (for complex logical reasoning, math, and coding) and **non-thinking mode** (for efficient, general-purpose chat) **within a single model**, ensuring optimal performance across various scenarios."
msgstr "支持在**思考模式**（用于复杂逻辑推理、数学和编码）和 **非思考模式** （用于高效通用对话）之间**无缝切换**，确保在各种场景下的最佳性能。"

#: ../../Qwen/source/index.rst:18 189ff2a03ad249ef88202c34e9f8aa86
msgid "**Significantly enhancement in reasoning capabilities**, surpassing previous QwQ (in thinking mode) and Qwen2.5 instruct models (in non-thinking mode) on mathematics, code generation, and commonsense logical reasoning."
msgstr "**显著增强的推理能力**，在数学、代码生成和常识逻辑推理方面超越了之前的 QwQ（在思考模式下）和 Qwen2.5 指令模型（在非思考模式下）。"

#: ../../Qwen/source/index.rst:19 64ebcda0381148cb8edf8d92b49469ea
msgid "**Superior human preference alignment**, excelling in creative writing, role-playing, multi-turn dialogues, and instruction following, to deliver a more natural, engaging, and immersive conversational experience."
msgstr "**卓越的人类偏好对齐**，在创意写作、角色扮演、多轮对话和指令跟随方面表现出色，提供更自然、更吸引人和更具沉浸感的对话体验。"

#: ../../Qwen/source/index.rst:20 ec0ebb91f1ed491f8672aefef6307d85
msgid "**Expertise in agent capabilities**, enabling precise integration with external tools in both thinking and unthinking modes and achieving leading performance among open-source models in complex agent-based tasks."
msgstr "**擅长智能体能力**，可以在思考和非思考模式下精确集成外部工具，在复杂的基于代理的任务中在开源模型中表现领先。"

#: ../../Qwen/source/index.rst:21 526b161edf284e1b913aabc7e7fcc77c
msgid "**Support of 100+ languages and dialects** with strong capabilities for **multilingual instruction following** and **translation**."
msgstr "**支持 100 多种语言和方言**，具有强大的多语言理解、推理、指令跟随和生成能力。"

#: ../../Qwen/source/index.rst:23 79ed3f0e7da043bb8b53f510ed244814
msgid "For more information, please visit our:"
msgstr "想了解更多信息，欢迎访问："

#: ../../Qwen/source/index.rst:25 b2e579ae57de4d2985ab1c350fdf2458
msgid "`Blog <https://qwenlm.github.io/>`__"
msgstr "`博客 <https://qwenlm.github.io/>`__"

#: ../../Qwen/source/index.rst:26 406389fe90064e879bd28665a021ee7e
msgid "`GitHub <https://github.com/QwenLM>`__"
msgstr "`GitHub <https://github.com/QwenLM>`__"

#: ../../Qwen/source/index.rst:27 714c64df6aed4e608571de0155199fef
msgid "`Hugging Face <https://huggingface.co/Qwen>`__"
msgstr "`Hugging Face <https://huggingface.co/Qwen>`__"

#: ../../Qwen/source/index.rst:28 214e12e0b1c04b268582b2c46d22334d
msgid "`ModelScope <https://modelscope.cn/organization/qwen>`__"
msgstr "`ModelScope <https://modelscope.cn/organization/qwen>`__"

#: ../../Qwen/source/index.rst:29 9c64e461dc3a440ab92d94887fe3d2d8
msgid "`Qwen3 Collection <https://huggingface.co/collections/Qwen/qwen3-67dd247413f0e2e4f653967f>`__"
msgstr ""

#: ../../Qwen/source/index.rst:31 c6056edc8a3a4a12bd3a75eeb210f7a2
msgid "Join our community by joining our `Discord <https://discord.gg/yPEP2vHTu4>`__ and `WeChat <https://github.com/QwenLM/Qwen/blob/main/assets/wechat.png>`__ group. We are looking forward to seeing you there!"
msgstr "加入社区，加入 `Discord <https://discord.gg/yPEP2vHTu4>`__ 和 `微信群 <https://github.com/QwenLM/Qwen/blob/main/assets/wechat.png>`__ 。很期待见到你们！"

#~ msgid "Web UI"
#~ msgstr "Web UI"

#~ msgid "Benchmark"
#~ msgstr "评测"

#~ msgid "Qwen2.5"
#~ msgstr ""

#~ msgid "Dense, easy-to-use, decoder-only language models, available in **0.5B**, **1.5B**, **3B**, **7B**, **14B**, **32B**, and **72B** sizes, and base and instruct variants."
#~ msgstr "易于使用的仅解码器稠密语言模型，提供 **0.5B** 、**1.5B** 、**3B** 、**7B** 、**14B** 、**32B** 和 **72B** 共7种参数规模的模型，并且有基模型和指令微调模型两种变体（其中“ B ”表示“十亿”， 72B 即为 720 亿）"

#~ msgid "Pretrained on our latest large-scale dataset, encompassing up to **18T** tokens."
#~ msgstr "利用我们最新的数据集进行预训练，包含多达 18T tokens （其中“ T ”表示“万亿”， 18T 即为 18 万亿）"

#~ msgid "Significant improvements in instruction following, generating long texts (over 8K tokens), understanding structured data (e.g, tables), and generating structured outputs especially JSON."
#~ msgstr "在遵循指令、生成长文本（超过 8K tokens ）、理解结构化数据（例如，表格）以及生成结构化输出特别是 JSON 方面有了显著改进"

#~ msgid "More resilient to the diversity of system prompts, enhancing role-play implementation and condition-setting for chatbots."
#~ msgstr "更加适应多样化的系统提示，增强了角色扮演的实现和聊天机器人的背景设置。"

#~ msgid "Context length support up to **128K** tokens and can generate up to **8K** tokens."
#~ msgstr "支持最多达 **128K** tokens 的上下文长度，并能生成多达 **8K** tokens 的文本。"

#~ msgid "Multilingual support for over **29** languages, including Chinese, English, French, Spanish, Portuguese, German, Italian, Russian, Japanese, Korean, Vietnamese, Thai, Arabic, and more."
#~ msgstr "支持超过 **29** 种语言，包括中文、英文、法文、西班牙文、葡萄牙文、德文、意大利文、俄文、日文、韩文、越南文、泰文、阿拉伯文等。"

#~ msgid "`Qwen2.5 Collection <https://huggingface.co/collections/Qwen/qwen25-66e81a666513e518adb90d9e>`__"
#~ msgstr ""

