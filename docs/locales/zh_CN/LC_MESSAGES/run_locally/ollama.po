# Copyright (C) 2024, Qwen Team, Alibaba Group.
# This file is distributed under the same license as the Qwen package.
#
msgid ""
msgstr ""
"Project-Id-Version: Qwen \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-29 16:34+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/run_locally/ollama.md:1 66b5f4776d0a45ec9a7ed2c147a6323e
msgid "Ollama"
msgstr "Ollama"

#: ../../source/run_locally/ollama.md:4 1a60e4f166184588802505205b51ea7a
msgid "To be updated for Qwen3."
msgstr "仍需为Qwen3更新。"

#: ../../source/run_locally/ollama.md:7 06925624447242879d12bcbc3be61ce6
#, fuzzy
msgid "[Ollama](https://ollama.com/) helps you run LLMs locally with only a few commands. It is available at macOS, Linux, and Windows. Now, Qwen2.5 is officially on Ollama, and you can run it with one command:"
msgstr "[Ollama](https://ollama.com/)帮助您通过少量命令即可在本地运行LLM。它适用于MacOS、Linux和Windows操作系统。现在，Qwen2.5正式上线Ollama，您只需一条命令即可运行它："

#: ../../source/run_locally/ollama.md:15 1180461df89c41c586d499daf5c6e0a0
msgid "Next, we introduce more detailed usages of Ollama for running Qwen2.5 models."
msgstr "接着，我们介绍在Ollama使用Qwen2.5模型的更多用法"

#: ../../source/run_locally/ollama.md:17 816ea502f0be44bcab371fd76e486618
msgid "Quickstart"
msgstr "快速开始"

#: ../../source/run_locally/ollama.md:19 c6d1c533593b495da62c73c8434636f8
msgid "Visit the official website [Ollama](https://ollama.com/) and click download to install Ollama on your device. You can also search models on the website, where you can find the Qwen2.5 models. Except for the default one, you can choose to run Qwen2.5-Instruct models of different sizes by:"
msgstr "访问官方网站[Ollama](https://ollama.com/)，点击`Download`以在您的设备上安装Ollama。您还可以在网站上搜索模型，在这里您可以找到Qwen2.5系列模型。除了默认模型之外，您可以通过以下方式选择运行不同大小的Qwen2.5-Instruct模型："

#: ../../source/run_locally/ollama.md:23 9f87565dcf4b44b8adce249c1863adbd
msgid "`ollama run qwen2.5:0.5b`"
msgstr ""

#: ../../source/run_locally/ollama.md:24 dcd5286c72a2440fb0fc7bfd210dd8d9
msgid "`ollama run qwen2.5:1.5b`"
msgstr ""

#: ../../source/run_locally/ollama.md:25 d2c13d08cad34893804497095676ddd4
msgid "`ollama run qwen2.5:3b`"
msgstr ""

#: ../../source/run_locally/ollama.md:26 e4a8e4cbd090469e9094afae3abe30f7
msgid "`ollama run qwen2.5:7b`"
msgstr ""

#: ../../source/run_locally/ollama.md:27 a97beb23d6074c2db90428b717ab61dd
msgid "`ollama run qwen2.5:14b`"
msgstr ""

#: ../../source/run_locally/ollama.md:28 049274657be149b4976d722cd608eb09
msgid "`ollama run qwen2.5:32b`"
msgstr ""

#: ../../source/run_locally/ollama.md:29 baec60a5ed5047e3bd080aa96cfc577b
msgid "`ollama run qwen2.5:72b`"
msgstr ""

#: ../../source/run_locally/ollama.md:32 2f9ef84694f5446f87db2333336f93c9
msgid "`ollama` does not host base models. Even though the tag may not have the instruct suffix, they are all instruct models."
msgstr "`ollama`并不托管基模型。即便模型标签不带instruct后缀，实际也是instruct模型。"

#: ../../source/run_locally/ollama.md:36 9545d728b8aa4163a66accdb54694caf
msgid "Run Ollama with Your GGUF Files"
msgstr "用Ollama运行你自己的GGUF文件"

#: ../../source/run_locally/ollama.md:38 f5a073220da84c19975adcce41f76d5c
msgid "Sometimes you don't want to pull models and you just want to use Ollama with your own GGUF files. Suppose you have a GGUF file of Qwen2.5, `qwen2.5-7b-instruct-q5_0.gguf`. For the first step, you need to create a file called `Modelfile`. The content of the file is shown below:"
msgstr "有时您可能不想拉取模型，而是希望直接使用自己的GGUF文件来配合Ollama。假设您有一个名为`qwen2.5-7b-instruct-q5_0.gguf`的Qwen2.5的GGUF文件。在第一步中，您需要创建一个名为`Modelfile`的文件。该文件的内容如下所示："

#: ../../source/run_locally/ollama.md:101 15e36d92505e4e21a2fe5dd07c5861e7
#, fuzzy
msgid "Then create the Ollama model by running:"
msgstr "然后通过运行下列命令来创建一个ollama模型"

#: ../../source/run_locally/ollama.md:107 4087d4c583fe44acb1456ca2412f8c85
#, fuzzy
msgid "Once it is finished, you can run your Ollama model by:"
msgstr "完成后，你即可运行你的ollama模型："

#: ../../source/run_locally/ollama.md:113 083503ce2e9d48eab757e8a4cf805d8d
msgid "Tool Use"
msgstr "工具调用"

#: ../../source/run_locally/ollama.md:115 b7635c6f3ccc4294a2ba4a45f17ae163
#, fuzzy
msgid "Tool use is now supported Ollama and you should be able to run Qwen2.5 models with it. For more details, see our [function calling guide](../framework/function_call)."
msgstr "Ollama现已支持工具调用，Qwen2.5也已适配。更多详情，请参阅我们的[函数调用指南](../framework/function_call)"

