# SOME DESCRIPTIVE TITLE.
# Copyright (C) 2024, Qwen Team
# This file is distributed under the same license as the Qwen package.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: Qwen \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-06 15:36+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: zh_CN\n"
"Language-Team: zh_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.17.0\n"

#: ../../source/framework/qwen_agent.rst:2 3198c4ad48b24f04adf4c8163aa43c08
msgid "Qwen-Agent"
msgstr "Qwen-Agent"

#: ../../source/framework/qwen_agent.rst:4 89ecd942286b4aa28ce432ce56200fc4
#, fuzzy
msgid "`Qwen-Agent <https://github.com/QwenLM/Qwen-Agent>`__ is a framework for developing LLM applications based on the instruction following, tool usage, planning, and memory capabilities of Qwen."
msgstr "`Qwen-Agent <https://github.com/QwenLM/Qwen-Agent>`__ 是一个基于 Qwen 的指令跟随、工具使用、计划和记忆能力来开发 LLM 应用程序的框架。它还附带了一些示例应用程序，例如浏览器助手、代码解释器和自定义助手。"

#: ../../source/framework/qwen_agent.rst:8 d50288d913d8477cbc9f2bde5bf43d69
msgid "This is the simplest tutorial on using Qwen-Agent to quickly experience the agentic capabilities of Qwen3. For more detailed information, please refer to `Qwen-Agent <https://github.com/QwenLM/Qwen-Agent>`__ repository."
msgstr ""

#: ../../source/framework/qwen_agent.rst:13 a47b6aea184a41a1adb5006d991c3fc1
msgid "Installation"
msgstr "安装"

#: ../../source/framework/qwen_agent.rst:15 f0ef1d0172da46e29ec2cc553bcdf0e2
msgid "Install the stable version from PyPI:"
msgstr ""

#: ../../source/framework/qwen_agent.rst:28 037c935faaf944239b5dd20af89330a5
msgid "Developing Your Own Agent"
msgstr "开发您自己的智能体"

#: ../../source/framework/qwen_agent.rst:30 1834b44a2c9442bc9bf392697acc1044
msgid "Qwen3 excels in tool calling capabilities. Qwen-Agent encapsulates tool-calling templates and tool-calling parsers internally, greatly reducing coding complexity."
msgstr ""

#: ../../source/framework/qwen_agent.rst:34 9c9b9e07388648ca9c2257e81558cfd2
msgid "To define the available tools, you can use the MCP configuration file, use the integrated tool of Qwen-Agent, or integrate other tools by yourself."
msgstr ""

#~ msgid "To be updated for Qwen3."
#~ msgstr "仍需为Qwen3更新。"

#~ msgid "Qwen-Agent provides atomic components such as LLMs and prompts, as well as high-level components such as Agents. The example below uses the Assistant component as an illustration, demonstrating how to add custom tools and quickly develop an agent that uses tools."
#~ msgstr "Qwen-Agent 提供包括语言模型和提示词等原子级组件，及智能体等高级组件在内的多种组件。以下示例选取助理组件进行展示，阐述了如何整合自定义工具以及如何迅速开发出一个能够应用这些工具的代理程序。"

#~ msgid "The framework also provides more atomic components for developers to combine. For additional showcases, please refer to `examples <https://github.com/QwenLM/Qwen-Agent/tree/main/examples>`__."
#~ msgstr "该框架还为开发者提供了更多的原子组件以供组合使用。欲了解更多示例，请参见 `examples <https://github.com/QwenLM/Qwen-Agent/tree/main/examples>`__。"

